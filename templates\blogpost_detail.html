
{% extends 'base.html' %}
{% block content %}
{% load crispy_forms_tags %}

{# Main container for blog post detail view #}
<div class="container col-lg-8 col-md-12 col-sm-12">
    <div class="masthead">
        {# Header section with post title and author info #}
        <div class="container card-body">
            <div class="row">
                {# Left column: Post metadata #}
                <div
                    class="col-lg-6 col-md-6 col-sm-12 masthead-text d-flex flex-column justify-content-center">
                    <h1 class="post-title">{{ blogpost.blog_title }}</h1>
                    <p class="post-subtitle mb-1">Author
                        <a
                        href="{% url 'other_user_profile' blogpost.author.username %}">{{ blogpost.author }}</a>
                        | {{ blogpost.created_on|date:"F d, Y" }}
                    </p>
                    {# Bookmark functionality for authenticated users #}
                    {% if user.is_authenticated %}
                    <form class="post-bookmark mb-2"
                        action="{% url 'bookmark_unbookmark' blogpost.slug %}"
                        method="POST">
                        {% csrf_token %}
                        {% if user in blogpost.bookmarks.all %}
                        <button type="submit" class="btn-bookmark">
                            <i class="fas fa-bookmark"></i> Unsave This Post
                        </button>
                        {% else %}
                        <button type="submit" class="btn-bookmark">
                            <i class="far fa-bookmark"></i> Save This Post
                        </button>
                        {% endif %}
                    </form>
                    {% endif %}
                </div>

                {# Right column: Featured image #}
                <div
                class="col-lg-6 col-md-6 col-sm-12 masthead-image d-flex align-items-center">
        
                    <img class="img-fluid"
                        src="{{ blogpost.featured_image.url }}"
                        alt="{{ blogpost.blog_title }} Image">
            
                </div>
            </div>
        </div>

        {# Post content section #}
        <div class="container col-sm-12">
            <div class="row">
                <div class="col card mb-6">
                    <div class="card-body">
                        {# Post excerpt and content #}
                        <p class="card-text">
                            <strong>{{ blogpost.excerpt }}</strong></p>
                        <hr>
                        <p class="card-text">{{ blogpost.content | safe }}</p>

                        {# Post metadata #}
                        <div class="mt-3">
                            <strong>Release Year:</strong>
                            {{ blogpost.release_year }}
                        </div>
                        <div class="mt-3">
                            <strong>Category:</strong>
                            {{ blogpost.media_category }}
                        </div>
                        {# Optional media link #}
                        {% if blogpost.media_link %}
                        <div class="mt-3 mb-3">
                            <strong>Media Link/Reference:</strong>
                            <a href="{{ blogpost.media_link }}"
                                target="_blank">{{ blogpost.media_link }}</a>
                        </div>
                        {% endif %}
                        {# Divider #}
                        <hr>
                        <div class="row">
                            {# Likes (Heart Icon and Count) #}
                            <div class="col-12 d-flex justify-content-between">
                                
                                    {% if user.is_authenticated %}
                                    <form class="d-inline"
                                        action="{% url 'like_unlike' blogpost.slug %}"
                                        method="POST">
                                        {% csrf_token %}
                                        {% if liked %}
                                        <button type="submit"
                                            name="blogpost_id"
                                            value="{{blogpost.slug}}"
                                            class="btn-like">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                        {% else %}
                                        <button type="submit"
                                            name="blogpost_id"
                                            value="{{blogpost.slug}}"
                                            class="btn-like">
                                            <i class="far fa-heart"></i>
                                        </button>
                                        {% endif %}
                                        {{ blogpost.number_of_likes }}
                                    </form>
                                    {% endif %}
                                
                                {# Comments (Comment Icon and Count) #}
                                {% with comments.count as total_comments %}
                                <strong class="text-secondary">
                                    <i class="far fa-comments ml-5"></i>
                                    {{ total_comments }}
                                </strong>
                                {% endwith %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col card mb-6">
                <div class="card-body">
                    <h3>Post a comment:</h3>
                    <p class="text-muted small">Your post will be displayed on
                        the bottom of the comment section below</p>
                        {# Comment Form #}
                        <form method="post" class="mt-3">
                        {{ comment_form | crispy }}
                        {% csrf_token %}
                        <button type="submit"
                            class="btn btn-signup btn-lg mt-3" id="submit_btn">Submit</button>
                    </form>
                </div>
            </div>
            <div class="row">
                <div class="col card mb-6 mt-3">
                    <div class="card-body">
                        <h3>Comments:</h3>
                        {% for comment in comments %}
                        <div class="comments">
                            <p class="font-weight-bold">
                                <a
                                href="{% url 'other_user_profile' comment.user.username %}">{{ comment.user.username }}</a>

                                <span class="text-muted font-weight-normal">
                                    {{ comment.created_on }}
                                </span> wrote:
                            </p>
                            {{ comment.body | linebreaks }}
                        </div>
                        <div class="row">
                            <div class="col">
                                <hr>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock content %}
